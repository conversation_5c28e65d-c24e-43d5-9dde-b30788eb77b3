apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component

# Database init container component with dynamic placeholders
# This component adds a database connectivity check init container to deployments
# All database connection details are extracted from dynamic placeholders
# Environment-specific configurations are applied via separate patches

patches:
- target:
    kind: Deployment
    name: ai-django-backend-test
  path: init-container-patch.yaml
