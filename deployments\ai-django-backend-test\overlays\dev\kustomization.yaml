apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-django-backend-test-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

components:
- ../../components/common-labels
- ../../components/database-init

labels:
- pairs:
    app: ai-django-backend-test
    app.kubernetes.io/name: ai-django-backend-test
    app.kubernetes.io/part-of: AI Django Backend
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/version: "f3ed37db"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting-ai-django-backend
    source.branch: main

# Environment-specific configurations are now directly in the manifest files
# Database init container environment-specific patch is handled by the database-init component

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: ai-django-backend-test
- path: database-init-dev-patch.yaml
  target:
    kind: Deployment
    name: ai-django-backend-test

namePrefix: ""
nameSuffix: "-dev"
