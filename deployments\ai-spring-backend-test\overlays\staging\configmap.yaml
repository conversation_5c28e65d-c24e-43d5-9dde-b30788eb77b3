apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-test-config
  labels:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/version: "85d91ebc"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "ai-spring-backend-test"
  PROJECT_ID: "ai-spring-backend-test"
  APPLICATION_TYPE: "springboot-backend"
  SOURCE_REPO: "ChidhagniConsulting-ai-spring-backend"
  SOURCE_BRANCH: "26-merge"
  COMMIT_SHA: "85d91ebc"
  NODE_ENV: "staging"
  PORT: "8080"
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:8080"
  API_URL: "http://localhost:8080/api"
  
  # Common Backend Configuration
  SERVER_PORT: "8080"

  
  # Spring Boot Configuration
  SPRING_PROFILES_ACTIVE: "staging"
  SPRING_APPLICATION_NAME: "app"

  # Spring Boot Database Configuration (Managed DigitalOcean Database)
  # SPRING_DATASOURCE_URL is configured via environment variables in deployment
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  # SPRING_DATASOURCE_USERNAME is configured via environment variables in deployment
  SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE: "10"
  SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE: "2"
  SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT: "30000"
  SPRING_DATASOURCE_HIKARI_IDLE_TIMEOUT: "600000"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "validate"
  SPRING_JPA_SHOW_SQL: "false"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "false"
  SPRING_JPA_PROPERTIES_HIBERNATE_JDBC_TIME_ZONE: "UTC"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"

  # JVM Configuration - Optimized for staging (2Gi container limit)
  JAVA_OPTS: "-Xms512m -Xmx1536m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"
  

  

  

  
