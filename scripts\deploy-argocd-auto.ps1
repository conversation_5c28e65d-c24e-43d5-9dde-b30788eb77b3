#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Automated ArgoCD deployment script for GitOps automation
    
.DESCRIPTION
    This script automatically detects new project folders and applies ArgoCD configurations
    to create applications in the ArgoCD dashboard. It integrates with the existing GitOps
    automation workflow that generates manifests from GitHub issues.
    
.PARAMETER ProjectName
    Specific project name to deploy. If not provided, will detect from current context.
    
.PARAMETER DryRun
    Show what would be deployed without actually executing kubectl commands.
    
.PARAMETER Validate
    Validate ArgoCD manifests before applying them.
    
.PARAMETER WaitForSync
    Wait for ArgoCD application to sync after deployment.

.PARAMETER Environment
    Target environment for deployment (dev, staging, production). Default: dev

.EXAMPLE
    ./deploy-argocd-auto.ps1 -ProjectName "my-app" -Environment "dev"

.EXAMPLE
    ./deploy-argocd-auto.ps1 -DryRun -Validate -Environment "staging"
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$ProjectName,

    [Parameter(Mandatory=$false)]
    [switch]$DryRun,

    [Parameter(Mandatory=$false)]
    [switch]$Validate,

    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "staging", "production")]
    [string]$Environment = "dev",

    [Parameter(Mandatory=$false)]
    [switch]$WaitForSync,
    
    [Parameter(Mandatory=$false)]
    [int]$SyncTimeoutSeconds = 300
)

# Function to check if kubectl is available and configured
function Test-KubectlAvailability {
    try {
        $null = kubectl version --client --short 2>$null
        Write-Host "✅ kubectl is available" -ForegroundColor Green
        
        # Test cluster connectivity
        $null = kubectl cluster-info --request-timeout=5s 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Kubernetes cluster is accessible" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️  kubectl is available but cluster is not accessible" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "❌ kubectl is not available or not configured" -ForegroundColor Red
        return $false
    }
}

# Function to validate ArgoCD manifest files
function Test-ArgoCDManifests {
    param(
        [string]$ProjectPath,
        [string]$Environment = "dev"
    )

    $projectFile = Join-Path $ProjectPath "argocd/project.yaml"
    # Applications are now environment-specific and located in overlays
    $applicationFile = Join-Path $ProjectPath "overlays/$Environment/application.yaml"

    $isValid = $true

    # Check if files exist
    if (-not (Test-Path $projectFile)) {
        Write-Host "❌ Project manifest not found: $projectFile" -ForegroundColor Red
        $isValid = $false
    }

    if (-not (Test-Path $applicationFile)) {
        Write-Host "❌ Application manifest not found: $applicationFile" -ForegroundColor Red
        Write-Host "ℹ️  Note: Applications are now environment-specific. Available environments:" -ForegroundColor Yellow
        $overlaysDir = Join-Path $ProjectPath "overlays"
        if (Test-Path $overlaysDir) {
            Get-ChildItem -Directory -Path $overlaysDir | ForEach-Object {
                Write-Host "  - $($_.Name)" -ForegroundColor Cyan
            }
        }
        $isValid = $false
    }
    
    if (-not $isValid) {
        return $false
    }
    
    # Validate YAML syntax
    try {
        $projectContent = Get-Content $projectFile -Raw
        $applicationContent = Get-Content $applicationFile -Raw
        
        # Basic YAML validation (check for required fields)
        if ($projectContent -match "kind:\s*AppProject" -and $projectContent -match "metadata:") {
            Write-Host "✅ Project manifest structure is valid" -ForegroundColor Green
        } else {
            Write-Host "❌ Project manifest structure is invalid" -ForegroundColor Red
            $isValid = $false
        }
        
        if ($applicationContent -match "kind:\s*Application" -and $applicationContent -match "metadata:") {
            Write-Host "✅ Application manifest structure is valid" -ForegroundColor Green
        } else {
            Write-Host "❌ Application manifest structure is invalid" -ForegroundColor Red
            $isValid = $false
        }
        
    } catch {
        Write-Host "❌ Error validating YAML files: $_" -ForegroundColor Red
        $isValid = $false
    }
    
    return $isValid
}

# Function to apply ArgoCD manifests
function Deploy-ArgoCDApplication {
    param(
        [string]$ProjectPath,
        [string]$ProjectName,
        [string]$Environment = "dev",
        [bool]$DryRunMode = $false
    )

    $projectFile = Join-Path $ProjectPath "argocd/project.yaml"
    # Applications are now environment-specific and located in overlays
    $applicationFile = Join-Path $ProjectPath "overlays/$Environment/application.yaml"
    
    Write-Host "`n🚀 Deploying ArgoCD application: $ProjectName" -ForegroundColor Green
    Write-Host "📁 Project path: $ProjectPath" -ForegroundColor Cyan
    
    if ($DryRunMode) {
        Write-Host "`n🔍 DRY RUN MODE - Commands that would be executed:" -ForegroundColor Yellow
        Write-Host "kubectl apply -f $projectFile" -ForegroundColor White
        Write-Host "kubectl apply -f $applicationFile" -ForegroundColor White
        return $true
    }
    
    # Apply ArgoCD Project
    Write-Host "`n📋 Applying ArgoCD Project..." -ForegroundColor Yellow
    try {
        kubectl apply -f $projectFile
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ ArgoCD Project applied successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to apply ArgoCD Project" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error applying ArgoCD Project: $_" -ForegroundColor Red
        return $false
    }
    
    # Apply ArgoCD Application
    Write-Host "`n🎯 Applying ArgoCD Application..." -ForegroundColor Yellow
    try {
        kubectl apply -f $applicationFile
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ ArgoCD Application applied successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to apply ArgoCD Application" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error applying ArgoCD Application: $_" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Function to wait for ArgoCD application sync
function Wait-ArgoCDSync {
    param(
        [string]$ApplicationName,
        [int]$TimeoutSeconds = 300
    )
    
    Write-Host "`n⏳ Waiting for ArgoCD application sync..." -ForegroundColor Yellow
    
    $startTime = Get-Date
    $timeout = $startTime.AddSeconds($TimeoutSeconds)
    
    while ((Get-Date) -lt $timeout) {
        try {
            $appStatus = kubectl get application $ApplicationName -n argocd -o jsonpath='{.status.sync.status}' 2>$null
            $healthStatus = kubectl get application $ApplicationName -n argocd -o jsonpath='{.status.health.status}' 2>$null
            
            if ($appStatus -eq "Synced" -and $healthStatus -eq "Healthy") {
                Write-Host "✅ Application is synced and healthy!" -ForegroundColor Green
                return $true
            } elseif ($appStatus -eq "Synced") {
                Write-Host "🔄 Application synced, waiting for health check..." -ForegroundColor Yellow
            } else {
                Write-Host "🔄 Application sync status: $appStatus, health: $healthStatus" -ForegroundColor Yellow
            }
            
            Start-Sleep -Seconds 10
        } catch {
            Write-Host "⚠️  Could not check application status, retrying..." -ForegroundColor Yellow
            Start-Sleep -Seconds 5
        }
    }
    
    Write-Host "⏰ Timeout waiting for application sync" -ForegroundColor Yellow
    return $false
}

# Function to detect project folders
function Get-ProjectFolders {
    $projectFolders = @()

    # Check if deployments directory exists
    $deploymentsDir = "deployments"
    if (-not (Test-Path $deploymentsDir)) {
        Write-Host "ℹ️  No deployments directory found" -ForegroundColor Yellow
        return $projectFolders
    }

    # Get all directories in deployments/ that contain argocd subfolder
    Get-ChildItem -Directory -Path $deploymentsDir | Where-Object {
        Test-Path (Join-Path $_.FullName "argocd")
    } | ForEach-Object {
        $projectFolders += @{
            Name = $_.Name
            Path = $_.FullName
        }
    }

    return $projectFolders
}

# Main execution
Write-Host "🚀 ArgoCD Automated Deployment Script" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host "🎯 Target Environment: $Environment" -ForegroundColor Cyan

# Check kubectl availability
if (-not (Test-KubectlAvailability)) {
    Write-Host "❌ Cannot proceed without kubectl access" -ForegroundColor Red
    exit 1
}

# Determine project to deploy
if ($ProjectName) {
    $projectPath = Join-Path "deployments" $ProjectName
    if (-not (Test-Path $projectPath)) {
        Write-Host "❌ Project folder not found: $projectPath" -ForegroundColor Red
        Write-Host "ℹ️  Looking for projects in deployments/ directory" -ForegroundColor Yellow
        exit 1
    }

    $projects = @(@{
        Name = $ProjectName
        Path = (Resolve-Path $projectPath).Path
    })
} else {
    # Auto-detect project folders
    $projects = Get-ProjectFolders
    
    if ($projects.Count -eq 0) {
        Write-Host "❌ No project folders with ArgoCD configurations found" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "📁 Detected $($projects.Count) project(s) with ArgoCD configurations:" -ForegroundColor Cyan
    $projects | ForEach-Object {
        Write-Host "  - $($_.Name)" -ForegroundColor White
    }
}

# Process each project
$successCount = 0
$totalCount = $projects.Count

foreach ($project in $projects) {
    Write-Host "`n" + "="*50 -ForegroundColor Gray
    Write-Host "Processing project: $($project.Name)" -ForegroundColor Cyan
    
    # Validate manifests if requested
    if ($Validate) {
        if (-not (Test-ArgoCDManifests -ProjectPath $project.Path -Environment $Environment)) {
            Write-Host "❌ Validation failed for project: $($project.Name)" -ForegroundColor Red
            continue
        }
    }

    # Deploy ArgoCD application
    if (Deploy-ArgoCDApplication -ProjectPath $project.Path -ProjectName $project.Name -Environment $Environment -DryRunMode $DryRun) {
        $successCount++
        
        # Wait for sync if requested and not in dry run mode
        if ($WaitForSync -and -not $DryRun) {
            Wait-ArgoCDSync -ApplicationName $project.Name -TimeoutSeconds $SyncTimeoutSeconds
        }
    }
}

# Summary
Write-Host "`n" + "="*50 -ForegroundColor Gray
Write-Host "🎉 Deployment Summary" -ForegroundColor Green
Write-Host "Successfully processed: $successCount/$totalCount projects" -ForegroundColor Cyan

if ($DryRun) {
    Write-Host "`n💡 This was a dry run. Use without -DryRun to actually deploy." -ForegroundColor Yellow
} else {
    Write-Host "`n🔗 Check ArgoCD dashboard to monitor your applications:" -ForegroundColor Cyan
    Write-Host "kubectl port-forward svc/argocd-server -n argocd 8080:443" -ForegroundColor White
    Write-Host "Then visit: https://localhost:8080" -ForegroundColor White
}

exit 0
