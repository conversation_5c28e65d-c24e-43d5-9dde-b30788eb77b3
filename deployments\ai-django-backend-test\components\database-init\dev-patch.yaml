apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-django-backend-test
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-database
        env:
        - name: ENVIRONMENT
          value: "dev"
        - name: TEST_CONNECTION
          value: "true"  # Enable connection testing in dev environment
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets-dev
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets-dev
              key: DB_PORT
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets-dev
              key: DB_USER
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets-dev
              key: DB_NAME
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets-dev
              key: DB_PASSWORD
        - name: PGSSLMO<PERSON>
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets-dev
              key: DB_SSL_MODE
