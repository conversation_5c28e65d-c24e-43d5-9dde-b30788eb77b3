apiVersion: apps/v1
kind: Deployment
metadata:
  name: P<PERSON><PERSON>HOLDER_PROJECT_ID
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-database
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: TEST_CONNECTION
          value: "true"  # Enable connection testing in production for faster startup
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: PLA<PERSON>H<PERSON><PERSON>R_PROJECT_ID-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_PORT
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_USER
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets
              key: DB_NAME
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: PLA<PERSON>H<PERSON>DER_PROJECT_ID-secrets
              key: DB_PASSWORD
        - name: PGSSLMODE
          valueFrom:
            secretKeyRef:
              name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_PROJECT_ID-secrets
              key: DB_SSL_MODE
